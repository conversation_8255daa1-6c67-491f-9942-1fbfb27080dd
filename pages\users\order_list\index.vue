<template>
  <view class="page-wrapper">
    <!-- 自定义顶部背景颜色 -->
    <!-- #ifdef MP || APP-PLUS -->
    <!-- #endif -->
    <view class="my-order">
      <view class="nav acea-row row-around" :class="isWhite ? 'bg-f' : ''">
        <view class="item" :class="orderStatus == -1 ? 'on' : ''" @click="statusClick(-1)">
          <view>全部</view>
          <!-- <view class="num">{{ orderData.all || 0 }}</view> -->
        </view>
        <view class="item" :class="orderStatus == 0 ? 'on' : ''" @click="statusClick(0)">
          <view>待付款</view>
          <!-- <view class="num">{{ orderData.noPay || 0 }}</view> -->
        </view>
        <view class="item" :class="orderStatus == 1 ? 'on' : ''" @click="statusClick(1)">
          <view>待核销</view>
          <!-- <view class="num">{{ orderData.noPostage || 0 }}</view> -->
        </view>
        <view class="item" :class="orderStatus == 2 ? 'on' : ''" @click="statusClick(2)">
          <view>待收货</view>
          <!-- <view class="num">{{ orderData.noDeliver || 0 }}</view> -->
        </view>
        <view class="item" :class="orderStatus == 3 ? 'on' : ''" @click="statusClick(3)">
          <view>待评价</view>
          <!-- <view class="num">{{ orderData.noComment || 0 }}</view> -->
        </view>
      </view>
      <view class="order-position">
        <!--定金预售订单-->
        <view
          v-if="presellProList.length > 0"
          class="event_container"
          :style="{ 'background-image': `url(${domain}/static/images/presell_orderBg.png)` }"
        >
          <navigator
            class="acea-row row-between"
            url="/pages/users/presell_order_list/index"
            hover-class="none"
          >
            <view class="info">
              <view class="title">预售尾款订单转到这里了！</view>
              <view class="desc">
                有
                <text class="t-color">{{ presellOrderCount }}</text>
                笔预售尾款订单待付款，请点击查看
              </view>
            </view>
            <view class="photo acea-row row-between">
              <view class="picture">
                <image
                  :src="
                    (presellProList[0].orderProduct[0].cart_info.productAttr &&
                      presellProList[0].orderProduct[0].cart_info.productAttr.image) ||
                    presellProList[0].orderProduct[0].cart_info.product.image
                  "
                ></image>
              </view>
              <view class="more_btn acea-row row-center row-middle">
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
            </view>
          </navigator>
        </view>
        <view class="list">
          <!-- 代付款 -->
          <view v-if="orderList.length > 0">
            <block v-if="orderStatus == 0">
              <view class="item" v-for="(item, index) in orderList" :key="index">
                <view @click="goOrderDetails(item.group_order_id)">
                  <view class="title acea-row row-between-wrapper">
                    <view class="acea-row row-middle left-wrapper">
                      {{ item.group_order_sn }}
                    </view>
                    <view>
                      {{
                        item.orderList[0].activity_type === 2 &&
                        item.orderList[0].orderProduct[0].cart_info.productPresell.presell_type == 2
                          ? '待付定金'
                          : '待付款'
                      }}
                    </view>
                  </view>
                  <view v-for="(order, j) in item.orderList" :key="order.order_id + j">
                    <!--预售-->
                    <view v-if="order.activity_type === 2">
                      <block v-for="(goods, g) in order.orderProduct">
                        <view class="item-info acea-row row-between row-top">
                          <view class="pictrue">
                            <image
                              :src="
                                (goods.cart_info.productAttr &&
                                  goods.cart_info.productAttr.image) ||
                                goods.cart_info.product.image
                              "
                            ></image>
                          </view>
                          <view class="text acea-row row-between">
                            <view class="name line1">
                              <text class="event_name event_bg">预售</text>
                              <text>{{ goods.cart_info.product.store_name }}</text>
                              <view class="event_ship event_color">
                                发货时间：
                                <!--全款预售-->
                                <text v-if="goods.cart_info.productPresell.presell_type === 1">
                                  {{
                                    goods.cart_info.productPresell.delivery_type === 1
                                      ? '支付成功后'
                                      : '预售结束后'
                                  }}{{ goods.cart_info.productPresell.delivery_day }}天内
                                </text>
                                <!--定金预售-->
                                <text v-if="goods.cart_info.productPresell.presell_type === 2">
                                  {{
                                    goods.cart_info.productPresell.delivery_type === 1
                                      ? '支付尾款后'
                                      : '预售结束后'
                                  }}{{ goods.cart_info.productPresell.delivery_day }}天内
                                </text>
                              </view>
                            </view>
                            <view class="money">
                              <view class="regular">
                                ¥{{ goods.cart_info.productPresellAttr.presell_price }}
                              </view>
                              <view>x{{ goods.product_num }}</view>
                            </view>
                          </view>
                          <view
                            v-if="goods.cart_info.productPresell.presell_type === 2"
                            class="event_price"
                          >
                            定金待支付
                            <text class="t-color">¥{{ order.pay_price }}</text>
                            尾款待支付
                            <text class="t-color">¥{{ order.presellOrder.pay_price }}</text>
                          </view>
                        </view>
                      </block>
                    </view>
                    <view v-else>
                      <block v-for="(goods, g) in order.orderProduct">
                        <view class="item-info acea-row row-between row-top">
                          <view class="pictrue">
                            <image
                              :src="
                                (goods.cart_info.productAttr &&
                                  goods.cart_info.productAttr.image) ||
                                goods.cart_info.product.image
                              "
                            ></image>
                          </view>
                          <view class="text acea-row row-between">
                            <view class="name line2">
                              <text
                                v-if="goods.product_type != 0 && goods.product_type != 10"
                                :class="'font_bg-red type' + goods.product_type"
                              >
                                {{
                                  goods.product_type == 1
                                    ? '秒杀'
                                    : goods.product_type == 2
                                    ? '预售'
                                    : goods.product_type == 3
                                    ? '助力'
                                    : goods.product_type == 4
                                    ? '拼团'
                                    : ''
                                }}
                              </text>
                              {{ goods.cart_info.product.store_name }}
                            </view>
                            <view class="money" v-if="item.orderList[0].activity_type == 4">
                              <view class="regular" v-if="goods.cart_info.activeSku">
                                ¥{{ goods.cart_info.activeSku.active_price }}
                              </view>
                              <view>x{{ goods.product_num }}</view>
                            </view>
                            <view v-else class="money">
                              <view class="regular">¥{{ goods.cart_info.productAttr.price }}</view>
                              <view>x{{ goods.product_num }}</view>
                            </view>
                          </view>
                        </view>
                      </block>
                    </view>
                  </view>

                  <view class="acea-row row-between time-wrap">
                    <view class="create-time">下单时间：{{ item.create_time }}</view>
                    <view
                      class="totalPrice acea-row row-right"
                      v-if="item.orderList[0].activity_type !== 2"
                    >
                      <text class="total-label">合计：</text>
                      <priceFormat
                        :price="item.pay_price"
                        weight
                        intSize="32"
                        floatSize="22"
                        labelSize="22"
                      ></priceFormat>
                    </view>
                  </view>

                  <view class="bottom acea-row row-right row-middle">
                    <view
                      class="bnt b-color"
                      @click.stop="goPay(item.pay_price, item.group_order_id)"
                    >
                      立即付款
                    </view>
                  </view>
                </view>
              </view>
            </block>
            <!-- 待发货 待收货 待评价 已完成 -->
            <block v-else>
              <view class="item" v-for="(item, index) in orderList" :key="index">
                <view @click="goOrderDetails(item.order_id)">
                  <view class="title acea-row row-between-wrapper">
                    <view class="acea-row row-middle left-wrapper" @click.stop="goMall(item)">
                      <text class="iconfont icon-ic_mall"></text>
                      <view class="store-name">{{ item.merchant.mer_name }}</view>
                      <text class="iconfont icon-ic_rightarrow"></text>
                    </view>
                    <view v-if="item.status == 0">
                      <text v-if="item.order_type == 1">
                        {{ item.takeOrderCount > 0 ? '部分核销' : '待核销' }}
                      </text>
                      <text v-else>待发货</text>
                    </view>
                    <view v-if="item.status == 1" class="t-color">待收货</view>
                    <view v-if="item.status == 2" class="t-color">待评价</view>
                    <view v-if="item.status == 3" class="t-color">已完成</view>
                    <view v-if="item.status == -1" class="t-color">已退款</view>
                  </view>
                  <view v-if="item.activity_type === 2">
                    <view
                      class="item-info acea-row row-between row-top"
                      v-for="(goods, index) in item.orderProduct"
                      :key="index"
                    >
                      <view class="pictrue">
                        <image
                          :src="
                            (goods.cart_info.productAttr && goods.cart_info.productAttr.image) ||
                            goods.cart_info.product.image
                          "
                        ></image>
                      </view>
                      <view class="text acea-row row-between">
                        <view class="name">
                          <view class="name" :class="item.status === 0 ? 'line1' : 'line2'">
                            <text class="event_name event_bg">预售</text>
                            <text>{{ goods.cart_info.product.store_name }}</text>
                            <view v-if="item.status == 0" class="event_ship event_color">
                              发货时间：
                              <!--全款预售-->
                              <text v-if="goods.cart_info.productPresell.presell_type === 1">
                                {{
                                  goods.cart_info.productPresell.delivery_type === 1
                                    ? '支付成功后'
                                    : '预售结束后'
                                }}{{ goods.cart_info.productPresell.delivery_day }}天内
                              </text>
                              <!--定金预售-->
                              <text v-if="goods.cart_info.productPresell.presell_type === 2">
                                {{
                                  goods.cart_info.productPresell.delivery_type === 1
                                    ? '支付尾款后'
                                    : '预售结束后'
                                }}{{ goods.cart_info.productPresell.delivery_day }}天内
                              </text>
                            </view>
                          </view>
                          <view class="t-color refund-text">
                            {{
                              goods.is_refund == 1
                                ? '退款中 ：商家待审核'
                                : goods.is_refund == 2
                                ? '部分退款'
                                : goods.is_refund == 3
                                ? '退款成功：退回至余额钱包'
                                : ''
                            }}
                          </view>
                        </view>
                        <view class="money">
                          <view class="regular">
                            ¥{{ goods.cart_info.productPresellAttr.presell_price }}
                          </view>
                          <view>x{{ goods.product_num }}</view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view v-else>
                    <view
                      class="item-info acea-row row-between row-top"
                      v-for="(goods, index) in item.orderProduct"
                      :key="index"
                    >
                      <view class="pictrue">
                        <image
                          :src="
                            (goods.cart_info.productAttr && goods.cart_info.productAttr.image) ||
                            goods.cart_info.product.image
                          "
                        ></image>
                      </view>
                      <view class="text acea-row row-between">
                        <view class="name">
                          <view class="name line2">
                            <text
                              v-if="goods.product_type != 0 && goods.product_type != 10"
                              :class="'font_bg-red type' + goods.product_type"
                            >
                              {{
                                goods.product_type == 1
                                  ? '秒杀'
                                  : goods.product_type == 2
                                  ? '预售'
                                  : goods.product_type == 3
                                  ? '助力'
                                  : goods.product_type == 4
                                  ? '拼团'
                                  : ''
                              }}
                            </text>
                            <text>{{ goods.cart_info.product.store_name }}</text>
                          </view>
                        </view>
                        <view class="money" v-if="item.activity_type == 3">
                          <view class="regular" v-if="goods.cart_info.productAssistAttr">
                            ¥{{ goods.cart_info.productAssistAttr.assist_price }}
                          </view>
                          <view>x{{ goods.product_num }}</view>
                        </view>
                        <view class="money" v-else-if="item.activity_type == 4">
                          <view class="regular" v-if="goods.cart_info.activeSku">
                            ¥{{ goods.cart_info.activeSku.active_price }}
                          </view>
                          <view>x{{ goods.product_num }}</view>
                        </view>
                        <view class="money" v-else>
                          <view class="regular">¥{{ goods.cart_info.productAttr.price }}</view>
                          <view>x{{ goods.product_num }}</view>
                        </view>
                      </view>

                      <view
                        class="refund-info"
                        v-if="
                          goods.is_refund === 0 || goods.is_refund === 2 || goods.is_refund === 3
                        "
                      >
                        <text class="refund-info-lable">退款成功：</text>
                        <text class="refund-amount">退回至余额钱包</text>
                        <text class="refund-price">¥{{ goods.product_price }}</text>
                      </view>
                      <view class="t-color refund-text">
                        {{
                          goods.is_refund == 1
                            ? '退款中 ：商家待审核'
                            : goods.is_refund == 2
                            ? '部分退款'
                            : goods.is_refund == 3
                            ? '退款成功：退回至余额钱包'
                            : ''
                        }}
                      </view>
                    </view>
                  </view>
                  <view class="acea-row row-between time-wrap">
                    <view class="create-time">下单时间：{{ item.create_time }}</view>
                    <view class="totalPrice acea-row row-right" v-if="item.activity_type == 2">
                      <text class="total-label">合计：</text>
                      <view class="money p-color">
                        <priceFormat
                          :price="item.presell_price"
                          weight
                          intSize="32"
                          floatSize="22"
                          labelSize="22"
                        ></priceFormat>
                      </view>
                    </view>
                    <view class="totalPrice acea-row row-right" v-else>
                      <text class="total-label">合计：</text>
                      <view class="money p-color">
                        <priceFormat
                          :price="item.pay_price"
                          weight
                          intSize="32"
                          floatSize="22"
                          labelSize="22"
                        ></priceFormat>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="bottom acea-row row-right row-middle">
                  <view
                    v-if="
                      !item.receipt &&
                      item.status != -1 &&
                      item.open_receipt == 1 &&
                      item.order_type !== 1
                    "
                    class="bnt cancelBnt"
                    @click.stop="applyInvoice(item.order_id)"
                  >
                    申请开票
                  </view>
                  <block v-if="item.status == 0 || item.status == 9 || item.status == -1">
                    <view class="bnt b-color" @click="goOrderDetails(item.order_id)">查看详情</view>
                  </block>
                  <block v-if="item.status == 1">
                    <navigator
                      class="bnt cancelBnt"
                      v-if="item.delivery_type == 1 || item.delivery_type == 2"
                      hover-class="none"
                      :url="'/pages/users/goods_logistics/index?orderId=' + item.order_id"
                    >
                      查看物流
                    </navigator>
                    <view class="bnt b-color" @tap="confirmOrder(item, index)">确认收货</view>
                  </block>
                  <block v-if="item.status == 2">
                    <navigator
                      v-if="community_status == 1 && !item.community_id"
                      :url="'/pages/plantGrass/plant_release/index?order_id=' + item.order_id"
                      class="bnt colorBnt"
                      hover-class="none"
                    >
                      <text class="iconfont icon-ic_edit"></text>
                      发布种草
                    </navigator>
                    <view class="bnt b-color" @click="goOrderDetails_Evaluation(item.order_id)">
                      去评价
                    </view>
                  </block>
                  <block v-if="item.status == 3">
                    <view
                      class="bnt b-color"
                      @click="goOrderDetails(item.order_id)"
                      v-if="
                        item.activity_type == 2 ||
                        item.activity_type == 3 ||
                        item.activity_type == 10
                      "
                    >
                      查看详情
                    </view>
                    <navigator
                      v-if="community_status == 1 && !item.community_id"
                      :url="'/pages/plantGrass/plant_release/index?order_id=' + item.order_id"
                      class="bnt colorBnt"
                      hover-class="none"
                    >
                      <text class="iconfont icon-ic_edit"></text>
                      发布种草
                    </navigator>
                    <view class="bnt b-color" @click="goOrderDetails(item.order_id)" v-else>
                      再次购买
                    </view>
                  </block>
                </view>
              </view>
            </block>
          </view>
        </view>
        <view class="loadingicon acea-row row-center-wrapper" v-if="orderList.length > 5">
          <text class="loading iconfont icon-jiazai" :hidden="loading == false"></text>
          {{ loadTitle }}
        </view>
        <view v-if="orderList.length == 0 && page > 1">
          <emptyPage
            title="暂无订单~"
            :noImage="`${domain}/static/images/noRecord.png`"
          ></emptyPage>
        </view>
      </view>
    </view>
    <payment
      :payMode="payMode"
      :pay_close="pay_close"
      @onChangeFun="onChangeFun"
      :order_id="pay_order_id"
      :totalPrice="totalPrice"
    ></payment>
    <addInvoicing
      ref="addInvoicing"
      @changeInvoiceClose="changeInvoiceClose"
      :invoice="invoice"
    ></addInvoicing>
  </view>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
let app = getApp()
let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
import {
  getOrderList,
  orderData,
  groupOrderList,
  orderTake,
  applyInvoiceApi,
  getOrderPayTypeList,
} from '@/api/order.js'
import { openOrderSubscribe } from '@/utils/SubscribeMessage.js'
import payment from '@/components/payment'
import addInvoicing from '@/components/addInvoicing'
import { mapGetters } from 'vuex'
import emptyPage from '@/components/emptyPage.vue'
import { configMap } from '@/utils'
import { HTTP_REQUEST_URL } from '@/config/app'
import { toLogin } from '@/libs/login.js'
export default {
  components: {
    payment,
    emptyPage,
    addInvoicing,
  },
  data() {
    return {
      loading: false, //是否加载中
      loadend: false, //是否加载完毕
      loadTitle: '加载更多', //提示语
      orderList: [], //订单数组
      presellProList: [], //定金预售订单
      presellOrderCount: 0,
      orderData: {}, //订单详细统计
      orderStatus: 0, //订单状态
      page: 1,
      limit: 20,
      domain: HTTP_REQUEST_URL,
      sysHeight: sysHeight,
      payMode: [],
      pay_close: false,
      pay_order_id: '',
      invoice_order_id: '',
      totalPrice: '0',
      isReady: true,
      invoice: {
        invoice: false,
        add: false,
      },
      isWhite: false,
    }
  },
  computed: {
    ...mapGetters(['isLogin', 'viewColor', 'orderPayList']),
    ...configMap(['hide_mer_status', 'community_status']),
  },
  watch: {
    orderPayList: {
      handler(nVal, oVal) {
        if (nVal) {
          this.payMode = nVal
        }
      },
      immediate: true,
      deep: true,
    },
  },
  onShow() {
    if (this.isLogin) {
      this.page = 1
      this.loadend = false
      this.loading = false
      this.pay_close = false
      this.getOrderData()
      this.getOrderList()
      this.getpreSellOrderList()
    } else {
      toLogin()
    }
  },
  onReady() {},
  mounted: function () {},
  onPageScroll(e) {
    if (e.scrollTop > 10) {
      this.isWhite = true
    } else {
      this.isWhite = false
    }
  },
  methods: {
    goBack: function () {
      uni.switchTab({
        url: '/pages/user/index',
      })
    },
    // 去商铺
    goMall(item) {
      if (this.hide_mer_status == 0) {
        uni.navigateTo({
          url: '/pages/store/home/<USER>' + item.mer_id,
        })
      }
    },
    /**
     * 事件回调
     *
     */
    onChangeFun: function (e) {
      let opt = e
      let action = opt.action || null
      let value = opt.value != undefined ? opt.value : null
      action && this[action] && this[action](value)
    },
    /**
     * 关闭支付组件
     *
     */
    payClose: function () {
      this.pay_close = false
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      if (options.status) this.orderStatus = options.status
    },
    /**
     * 获取订单统计数据
     *
     */
    getOrderData: function () {
      let that = this
      orderData().then((res) => {
        that.$set(that, 'orderData', res.data)
      })
    },
    /**
     * 打开支付组件
     *
     */
    goPay: function (pay_price, order_id) {
      this.order_id = order_id
      this.pay_order_id = order_id.toString()
      this.$set(this, 'totalPrice', pay_price)
      this.$util.getPayTypeList(this.pay_order_id, 0)
      this.$set(this, 'pay_close', true)
    },
    /**
     * 支付成功回调
     *
     */
    pay_complete: function () {
      this.loadend = false
      this.page = 1
      this.$set(this, 'orderList', [])
      this.pay_close = false
      this.pay_order_id = ''
      this.getOrderData()
      this.getOrderList()
    },
    /**
     * 支付失败回调
     *
     */
    pay_fail: function () {
      this.pay_close = false
      this.pay_order_id = ''
    },
    /**
     * 去订单详情
     */
    goOrderDetails: function (order_id) {
      let self = this
      if (!order_id)
        return that.$util.Tips({
          title: '缺少订单号无法查看订单详情',
        })
      // #ifdef MP
      uni.showLoading({
        title: '正在加载',
      })
      openOrderSubscribe()
        .then(() => {
          uni.hideLoading()
          if (self.orderStatus == 0) {
            uni.navigateTo({
              url: '/pages/order_details/stay?order_id=' + order_id,
            })
          } else {
            uni.navigateTo({
              url: '/pages/order_details/index?order_id=' + order_id,
            })
          }
        })
        .catch(() => {
          uni.hideLoading()
        })
      // #endif
      // #ifndef MP
      if (self.orderStatus == 0) {
        uni.navigateTo({
          url: '/pages/order_details/stay?order_id=' + order_id,
        })
      } else {
        uni.navigateTo({
          url: '/pages/order_details/index?order_id=' + order_id,
        })
      }
      // #endif
    },
    /**
     * 点击去评价
     */
    goOrderDetails_Evaluation: function (order_id) {
      let self = this
      if (!order_id)
        return that.$util.Tips({
          title: '缺少订单号无法查看订单详情和评价',
        })
      uni.navigateTo({
        url: '/pages/order_details/index?order_id=' + order_id,
      })
    },
    /**
     * 切换类型
     */
    statusClick: function (status) {
      if (status == this.orderStatus) return
      this.orderStatus = status
      this.loadend = false
      this.loading = false
      this.page = 1
      this.$set(this, 'orderList', [])
      this.getOrderList()
    },
    /**
     * 获取订单列表
     */
    getOrderList: function () {
      let that = this
      if (that.loadend) return
      if (that.loading) return
      that.loading = true
      that.loadTitle = '加载更多'
      uni.showLoading({
        title: '加载中',
        mask: true,
      })
      if (that.isReady) {
        that.isReady = false
        if (that.orderStatus == 0) {
          groupOrderList({
            page: that.page,
            limit: that.limit,
          }).then((res) => {
            that.isReady = true
            uni.hideLoading()
            let list = res.data.list || []
            let loadend = list.length < that.limit
            that.orderList = that.page == 1 ? list : that.$util.SplitArray(list, that.orderList)
            that.$set(that, 'orderList', that.orderList)
            that.getProductCount()
            that.loadend = loadend
            that.loading = false
            that.loadTitle = loadend ? '我也是有底线的' : '加载更多'
            that.page = that.page + 1
          })
        } else {
          getOrderList({
            status: that.orderStatus - 1,
            page: that.page,
            limit: that.limit,
          })
            .then((res) => {
              uni.hideLoading()
              let list = res.data.list || []
              let loadend = list.length < that.limit
              that.orderList = that.page == 1 ? list : that.$util.SplitArray(list, that.orderList)
              that.$set(that, 'orderList', that.orderList)
              that.getProductCount()
              that.loadend = loadend
              that.loading = false
              that.loadTitle = loadend ? '我也是有底线的' : '加载更多'
              that.page = that.page + 1
              that.isReady = true
            })
            .catch((err) => {
              that.loading = false
              that.loadTitle = '加载更多'
            })
        }
      }
    },
    /**
     * 获取定金预售订单列表
     */
    getpreSellOrderList: function () {
      let that = this
      getOrderList({
        status: 10,
        page: 1,
        limit: 1,
      }).then((res) => {
        let list = res.data.list || []
        that.presellOrderCount = res.data.count
        that.$set(that, 'presellProList', list)
      })
    },
    /**
     * 获取单个订单商品数量
     */
    getProductCount: function () {
      if (this.orderStatus !== 0) {
        this.orderList.forEach((item, i) => {
          let orderNum = 0
          if (item.orderProduct) {
            item.orderProduct.forEach((val) => {
              orderNum += val.product_num
            })
            this.orderList[i]['orderNum'] = orderNum
          }
        })
      }
    },
    // 确认收货
    confirmOrder: function (item, index) {
      let that = this
      uni.showModal({
        title: '确认收货',
        content: '为保障权益，请收到货确认无误后，再确认收货',
        success: function (res) {
          if (res.confirm) {
            orderTake(item.order_id)
              .then((res) => {
                return that.$util.Tips(
                  {
                    title: '操作成功',
                    icon: 'success',
                  },
                  function () {
                    that.orderList.splice(index, 1)
                    that.getOrderData()
                  },
                )
              })
              .catch((err) => {
                return that.$util.Tips({
                  title: err,
                })
              })
          }
        },
      })
    },
    /*申请开票*/
    applyInvoice(order_id) {
      let that = this
      that.invoice_order_id = order_id
      that.invoice.invoice = true
      that.$refs.addInvoicing.popupTitle = false
      that.$refs.addInvoicing.getInvoiceDefault()
      that.$refs.addInvoicing.getInvoiceList()
    },
    // 关闭发票弹窗
    changeInvoiceClose: function (data) {
      if (data) this.getInvoiceData(data)
      this.$set(this.invoice, 'invoice', false)
    },
    // 开票回调
    getInvoiceData(data) {
      let that = this
      applyInvoiceApi(that.invoice_order_id, data)
        .then((res) => {
          that.loadend = false
          that.loading = false
          that.page = that.page - 1
          that.getOrderList()
          return that.$util.Tips({
            title: res.message,
          })
        })
        .catch((err) => {
          return that.$util.Tips({
            title: err,
          })
        })
    },
  },
  onReachBottom: function () {
    this.getOrderList()
  },
}
</script>

<style scoped lang="scss">
.page-wrapper {
  --view-theme: #c9a063;
  --view-bntColor21: #c9a063;
  --view-bntColor22: #fde7d1;
}
.t-color {
  color: var(--view-theme);
}
.p-color {
  color: var(--view-priceColor);
}
.b-color {
  background-color: var(--view-theme);
}
.my-order .header .picTxt {
  height: 190rpx;
}
.my-order .header .picTxt .text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
}
.my-order .header .picTxt .text .name {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}
.my-order .header .picTxt .pictrue {
  width: 122rpx;
  height: 109rpx;
}
.my-order .header .picTxt .pictrue image {
  width: 100%;
  height: 100%;
}
.my-order .order-position {
  position: relative;
  padding-bottom: 0;
  padding-bottom: calc(0rpx+ constant(safe-area-inset-bottom));
  padding-bottom: calc(0rpx + env(safe-area-inset-bottom));
}
.my-order .nav {
  position: sticky;
  left: 0;
  top: 0;
  z-index: 9;
  padding: 20rpx 0;
}
.my-order .nav .item {
  text-align: center;
  font-size: 26rpx;
  color: #333333;
  padding: 10rpx 16rpx;
}
.my-order .nav .item.on {
  font-weight: bold;
  background: #c9a063;
  border-radius: 30rpx;
  color: white;
}
.my-order .nav .item .num {
  margin-top: 18rpx;
}
.my-order .list {
  width: 690rpx;
  margin: 20rpx auto 0 auto;
}
.my-order .list .item {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.my-order .list .item .title {
  height: 84rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #282828;
  .left-wrapper {
    .iconfont {
      font-size: 28rpx;
    }
    .store-name {
      margin: 0 10rpx;
    }
    .icon-ic_rightarrow {
      font-size: 20rpx;
    }
  }
}
.my-order .list .item .title .sign {
  font-size: 24rpx;
  padding: 0 7rpx;
  height: 36rpx;
  margin-right: 15rpx;
}
.my-order .list .item .item-info {
  padding: 0 30rpx;
  margin-top: 22rpx;
}
.my-order .list .item .item-info .pictrue {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}
.my-order .list .item .item-info .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.my-order .list .item .item-info .text {
  width: 486rpx;
  font-size: 28rpx;
  color: #999;
  margin-top: 6rpx;
}
.my-order .list .item .item-info .text .name {
  width: 320rpx;
  color: #282828;
}
.my-order .list .item .item-info .refund-text {
  margin-top: 10rpx;
  font-size: 22rpx;
}
.refund-info {
  margin-top: 10rpx;
  width: 100%;
  background: #f5f3ef;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  border-radius: 8rpx;
  padding: 16rpx 18rpx;
  margin-bottom: 20rpx;
  .refund-info-lable {
    color: #333333;
    font-weight: 500;
  }
  .refund-price {
    color: #c9a063;
    margin-left: 8rpx;
  }
  .refund-amount {
    color: #999;
    margin-left: 6rpx;
  }
}
.my-order .list .item .item-info .text .name .font_bg-red {
  position: relative;
  top: -2px;
}
.event_bg {
  background: #ff7f00;
}
.event_color {
  color: #ff7f00;
}
.my-order .list .item .event_name {
  display: inline-block;
  margin-right: 9rpx;
  color: #fff;
  font-size: 20rpx;
  padding: 0 8rpx;
  line-height: 30rpx;
  text-align: center;
  border-radius: 6rpx;
}
.my-order .list .item .event_ship {
  font-size: 20rpx;
  margin-top: 10rpx;
}
.my-order .list .event_price {
  margin: 0 0 50rpx 120rpx;
}
.my-order .list .item .item-info .text .money {
  text-align: right;
}
.time-wrap {
  padding: 20rpx;
}
.my-order .list .item .totalPrice {
  font-size: 26rpx;
  color: #282828;
  text-align: right;
  padding: 0 30rpx 30rpx 0;
}
.total-label {
  color: #999999;
}
.my-order .list .item .totalPrice .money {
  font-size: 28rpx;
  margin-left: 6rpx;
}
.my-order .list .item .bottom {
  height: 107rpx;
  padding-right: 30rpx;
  margin-left: 30rpx;
}
.my-order .list .item .bottom .bnt {
  width: 176rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border-radius: 50rpx;
  font-size: 27rpx;
  font-size: 24rpx;
}
.my-order .list .item .bottom .bnt.cancelBnt {
  border: 1px solid #ddd;
  color: #666666;
}
.my-order .list .item .bottom .bnt.colorBnt {
  border: 1px solid var(--view-theme);
  color: var(--view-theme);
}
.my-order .list .item .bottom .bnt .icon-ic_edit {
  font-size: 26rpx;
  margin-right: 10rpx;
}
.my-order .list .item .bottom .bnt ~ .bnt {
  margin-left: 17rpx;
}
.noCart {
  margin-top: 171rpx;
  padding-top: 0.1rpx;
}
.noCart .pictrue {
  width: 414rpx;
  height: 336rpx;
  margin: 78rpx auto 56rpx auto;
}
.noCart .pictrue image {
  width: 100%;
  height: 100%;
}
.event_container {
  width: 690rpx;
  background-size: cover;
  background-repeat: no-repeat;
  margin: 20rpx auto;
  padding: 30rpx 30rpx 24rpx;
  border-radius: 16rpx;
  .info {
    width: 420rpx;
    .title {
      color: #282828;
      font-size: 26rpx;
      font-weight: 500;
    }
    .desc {
      color: #999;
      font-size: 24rpx;
      margin-top: 16rpx;
    }
  }
  .photo {
    width: 144rpx;
    .picture {
      width: 76rpx;
      height: 76rpx;
      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
    }
    .more_btn {
      color: #fff;
      background: #f97e3b;
      width: 40rpx;
      height: 40rpx;
      border-radius: 40rpx;
      text-align: center;
      position: relative;
      top: 18rpx;
      text {
        font-size: 20rpx;
      }
    }
  }
}
.create-time {
  font-weight: 300;
  font-size: 26rpx;
  color: #999999;
}
</style>
