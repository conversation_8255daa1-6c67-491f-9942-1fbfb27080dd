<template>
  <view class="exchange-store">
    <!-- 御享值 -->
    <view class="z-10">
      <view class="exchange-store__top">
        <view class="exchange-store__coin">
          <image
            class="exchange-store__coin-img"
            src="/static/images/icon-jifen.png"
            mode="aspectFill"
          />
          <view class="">
            <view class="exchange-store__coin-label">
              我的御享值
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
            <view class="exchange-store__score">
              {{ userInfo.integral ? userInfo.integral : 0 }}
            </view>
          </view>
        </view>
        <button
          class="exchange-store__history-btn"
          hover-class="btnHoverClass"
          @click="toHistoryPage"
        >
          兑换记录
        </button>
      </view>
      <!-- 商品列表 -->
      <view class="product-list" v-if="goodList.length">
        <view
          class="product-item"
          v-for="(item, index) in goodList"
          :key="item.product_id"
          @click="goGoodsDetail(item)"
        >
          <view class="pictrue">
            <image :src="item.image"></image>
            <view v-if="item.stock == 0" class="sell_out">已兑完</view>
          </view>
          <view class="info">
            <view class="title line1">{{ item.store_name }}</view>
            <view class="acea-row price-count">
              <view class="sales">{{ parseFloat(Number(item.price).toFixed(2)) }}元</view>
              <text class="price">+{{ item.ot_price }}</text>
              <text class="count-text">积分</text>
              <button hover-class="button-hover" class="exchange-btn">兑换</button>
            </view>
          </view>
        </view>
      </view>
      <view v-else-if="!loading" class="no-goods">
        <image :src="`${domain}/static/images/no_thing.png`"></image>
        <view class="fontimg">暂无商品，去看点别的吧</view>
      </view>
      <view v-if="loading" class="loadingicon acea-row row-center-wrapper">
        <text class="loading iconfont icon-jiazai" :hidden="loading == false"></text>
        {{ loadTitle }}
      </view>
    </view>
  </view>
</template>

<script>
import { HTTP_REQUEST_URL } from '@/config/app'
import { getIntegralGoodsList } from '@/api/points_mall'
import { getUserInfo } from '@/api/user'
export default {
  name: 'ExchangeStore',
  data() {
    return {
      domain: HTTP_REQUEST_URL,
      goodList: [],
      loading: false,
      loadend: false,
      loadTitle: '',
      page: 1,
      limit: 10,
      userInfo: {},
    }
  },
  onLoad(options) {
    this.getIntegralGoods()
    this.getUserInfo()
  },
  methods: {
    toHistoryPage() {
      uni.navigateTo({
        url: '/pages/users/order_list/index',
      })
    },
    //积分商品列表
    getIntegralGoods() {
      let that = this
      if (that.loadend) return
      if (that.loading) return
      that.loading = true
      that.loadTitle = ''
      getIntegralGoodsList(that.where)
        .then((res) => {
          let list = res.data.list
          let goodList = that.$util.SplitArray(list, that.goodList)
          let loadend = list.length < that.where.limit
          that.loadend = loadend
          that.loading = false
          that.loadTitle = loadend ? '已全部加载' : '加载更多'
          that.$set(that, 'goodList', goodList)
        })
        .catch((err) => {
          that.loading = false
          uni.showToast({
            title: err,
            icon: 'none',
          })
        })
    },
    // 去商品详情
    goGoodsDetail(item) {
      uni.navigateTo({
        url: `/pages/points_mall/integral_goods_details?id=${item.product_id}`,
      })
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo() {
      getUserInfo().then((res) => {
        this.userInfo = res.data
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.exchange-store {
  --view-theme: #c9a063;
  min-height: 100vh;
  // background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 600rpx;
    background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  }
  padding: 28rpx;
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  &__coin {
    display: flex;
    align-items: center;
  }
  &__coin-img {
    width: 68rpx;
    height: 68rpx;
    margin-right: 18rpx;
    vertical-align: middle;
  }
  &__coin-label {
    font-size: 26rpx;
    color: #333;
    margin-right: 4rpx;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
    .iconfont {
      color: #666;
      font-size: 22rpx;
      padding-top: 4rpx;
    }
  }

  &__score {
    font-weight: 600;
    font-size: 36rpx;
    color: #333333;
  }
  &__history-btn {
    font-weight: 500;
    font-size: 24rpx;
    color: #ffffff;
    background: #c9a063;
    border-radius: 36rpx;
    padding: 12rpx 28rpx;
  }
  .product-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 30rpx;
    .product-item {
      position: relative;
      width: 330rpx;
      background: #fff;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      .pictrue {
        position: relative;
        width: 100%;
        height: 330rpx;
        .sell_out {
          display: flex;
          width: 150rpx;
          height: 150rpx;
          align-items: center;
          justify-content: center;
          border-radius: 100%;
          background: rgba(0, 0, 0, 0.6);
          color: #fff;
          font-size: 30rpx;
          position: absolute;
          top: 50%;
          left: 50%;
          margin: -75rpx 0 0 -75rpx;
          &::before {
            content: '';
            display: block;
            width: 140rpx;
            height: 140rpx;
            border-radius: 100%;
            border: 1px dashed #fff;
            position: absolute;
            top: 5rpx;
            left: 5rpx;
          }
        }
      }
      image {
        width: 100%;
        height: 340rpx;
        border-radius: 18rpx;
      }
      .info {
        padding: 20rpx;
        .title {
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
        }
        .price-count {
          margin-top: 8rpx;
          align-items: baseline;
          font-weight: 500;
        }
        .price {
          color: var(--view-theme);
          font-size: 28rpx;
        }
        .count-text {
          font-size: 22rpx;
          color: var(--view-theme);
        }
        .sales {
          font-size: 28rpx;
          color: var(--view-theme);
        }
        .exchange-btn {
          margin-left: auto;
          font-weight: 400;
          font-size: 26rpx;
          color: #ffffff;
          padding: 6rpx 20rpx;
          border-radius: 20rpx;
          background: var(--view-theme);
        }
      }
    }
  }
}
.z-10 {
  position: relative;
  z-index: 10;
}
.no-goods {
  display: flex;
  flex-direction: column;
  padding: 60rpx 0;
  image {
    width: 414rpx;
    height: 305rpx;
    display: block;
    margin: 0 auto;
  }
  .fontimg {
    text-align: center;
    color: #bebebe;
  }
}
</style>
